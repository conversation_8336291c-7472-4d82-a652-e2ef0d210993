import { StyleSheet } from 'react-native';
import type { Theme } from './theme/colors';
import { colors } from './theme/colors';
import { tokens } from './theme/tokens';

// Theme-aware card styles factory
export const createCardStyles = (theme: Theme) => StyleSheet.create({
  container: {
    backgroundColor: theme.surface,
    borderRadius: tokens.radii.card,
    padding: tokens.spacing.lg,
    marginBottom: tokens.spacing.xl,
    shadowColor: theme.shadow,
    ...tokens.shadows.card,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing.md,
  },
  title: {
    fontSize: tokens.typography.fontSize.xl,
    fontWeight: tokens.typography.fontWeight.heading,
    color: theme.textPrimary,
    marginBottom: tokens.spacing.md,
  },
  subtitle: {
    fontSize: tokens.typography.fontSize.md,
    color: theme.textSecondary,
    marginBottom: tokens.spacing.sm,
  },
});

// Legacy export for backward compatibility (uses light theme)
import { lightTheme } from './theme/colors';
export const cardStyles = createCardStyles(lightTheme);

// Common button styles
export const buttonStyles = StyleSheet.create({
  primary: {
    borderRadius: tokens.radii.button,
    overflow: 'hidden',
  },
  gradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: tokens.spacing.md,
    paddingHorizontal: tokens.spacing.xl,
  },
  text: {
    color: colors.textInverse,
    fontSize: tokens.typography.fontSize.lg,
    fontWeight: tokens.typography.fontWeight.heading,
    marginRight: tokens.spacing.xs,
  },
});

// Common input styles
export const inputStyles = StyleSheet.create({
  container: {
    borderWidth: tokens.borders.medium,
    borderColor: colors.borderLight,
    borderRadius: tokens.radii.input,
    padding: tokens.spacing.md,
    fontSize: tokens.typography.fontSize.md,
    backgroundColor: colors.backgroundSecondary,
    marginBottom: tokens.spacing.lg,
    minHeight: tokens.sizes.input.lg,
  },
  list: {
    minHeight: tokens.sizes.input.xl,
  },
});

// Common avatar styles
export const avatarStyles = StyleSheet.create({
  small: {
    width: tokens.sizes.avatar.sm,
    height: tokens.sizes.avatar.sm,
    borderRadius: tokens.sizes.avatar.sm / 2,
    backgroundColor: tokens.glassMorphism.light.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: tokens.spacing.xs,
  },
  medium: {
    width: tokens.sizes.avatar.md,
    height: tokens.sizes.avatar.md,
    borderRadius: tokens.sizes.avatar.md / 2,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: tokens.spacing.sm,
  },
});

// Common text styles
export const textStyles = StyleSheet.create({
  primary: {
    fontSize: tokens.typography.fontSize.lg,
    fontWeight: tokens.typography.fontWeight.heading,
    color: colors.textPrimary,
  },
  secondary: {
    fontSize: tokens.typography.fontSize.md,
    color: colors.textSecondary,
    lineHeight: tokens.typography.lineHeight.normal,
  },
  caption: {
    fontSize: tokens.typography.fontSize.sm,
    color: colors.textTertiary,
    lineHeight: tokens.typography.lineHeight.tight,
  },
  label: {
    fontSize: tokens.typography.fontSize.sm,
    color: colors.textSecondary,
    textTransform: 'uppercase',
    letterSpacing: tokens.typography.letterSpacing.wide,
    marginBottom: tokens.spacing.xs,
  },
});

// Common animation configurations
export const animationConfig = {
  fast: { duration: tokens.animations.duration.fast, useNativeDriver: true },
  medium: { duration: tokens.animations.duration.medium, useNativeDriver: true },
  slow: { duration: tokens.animations.duration.slow, useNativeDriver: true },
};
