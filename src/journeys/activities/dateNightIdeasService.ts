import { z } from 'zod';
import { supabase } from '../../shared/services/supabase/client';
import { Tables } from '../../shared/types/supabase.types';
import { logger } from '../../shared/utils/logger';
import { sanitizeSQLSearch } from '../utils/validation';

// Runtime validation for rows from Supabase
const DateNightRowSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().default(''),
  emoji: z.string().optional(),
  category: z.string().optional(),
  difficulty: z.enum(['easy','medium','hard']).optional(),
  estimated_duration: z.number().int().optional(),
  cost: z.enum(['free','low','medium','high']).optional(),
  indoor_outdoor: z.enum(['indoor','outdoor','both']).optional(),
  user_id: z.string().optional(),
  week_number: z.number().int().optional(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
});

// Create a proper interface that maps database properties to expected camelCase names
export interface DateNightIdea {
  id: string;
  title: string;
  description: string;
  category?: string | null;
  difficulty?: string | null;
  emoji?: string | null;
  estimatedDuration?: number | null; // mapped from estimated_duration
  costLevel?: string | null; // mapped from cost
  indoorOutdoor?: string | null; // mapped from indoor_outdoor
  user_id?: string | null; // mapped from user_id
  weekNumber?: number | null; // mapped from week_number
  source?: string | null;
  slug?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
  // For favorites system
  composite_id?: string; // computed property
}

// Use the database type from Supabase
export type UserDateNightIdea = Tables<'date_night_ideas_user'>;

class DateNightIdeasService {
  /**
   * Create composite ID for favorites system
   * Format: "global:{id}" or "user:{id}"
   */
  private createCompositeId(id: string, source: 'global' | 'user'): string {
    return `${source}:${id}`;
  }

  /**
   * Parse composite ID back to source and original ID
   */
  private parseCompositeId(compositeId: string): { source: 'global' | 'user'; id: string } {
    const [source, id] = compositeId.split(':');
    if (!source || !id || (source !== 'global' && source !== 'user')) {
      throw new Error(`Invalid composite ID format: ${compositeId}`);
    }
    return { source: source as 'global' | 'user', id };
  }

  /**
   * Transform database record to DateNightIdea with composite ID
   */
  private transformToDateNightIdea(record: any, source: 'global' | 'user'): DateNightIdea {
    const parsed = DateNightRowSchema.safeParse(record);
    if (!parsed.success) {
      logger.error('Invalid date night idea row', parsed.error.flatten());
      throw new Error('Invalid date night idea row');
    }
    const r = parsed.data;
    return {
      id: r.id,
      title: r.title,
      description: r.description,
      emoji: r.emoji,
      category: r.category,
      difficulty: r.difficulty,
      estimatedDuration: r.estimated_duration,
      costLevel: r.cost,
      indoorOutdoor: r.indoor_outdoor,
      user_id: r.user_id,
      weekNumber: r.week_number,
      source,
      composite_id: `${source}:${r.id}`,
      created_at: r.created_at,
      updated_at: r.updated_at,
    };
  }

  /**
   * Get demo date night ideas as fallback
   */
  private getDemoIdeas(): DateNightIdea[] {
    const demoData = [
      {
        id: 'demo-1',
        title: 'Romantic Dinner at Home',
        description: 'Cook a special meal together and dine by candlelight',
        category: 'romantic',
        cost: 'medium',
        difficulty: 'easy',
        indoor_outdoor: 'indoor',
        estimated_duration: 120,
        emoji: '🕯️',
        week_number: 1,
        source: 'weekly'
      },
      {
        id: 'demo-2',
        title: 'Stargazing Adventure',
        description: 'Find a quiet spot and watch the stars together',
        category: 'outdoor',
        cost: 'free',
        difficulty: 'easy',
        indoor_outdoor: 'outdoor',
        estimated_duration: 90,
        emoji: '⭐',
        week_number: 2,
        source: 'weekly'
      },
      {
        id: 'demo-3',
        title: 'Coffee Shop Date',
        description: 'Visit a cozy coffee shop and enjoy conversation',
        category: 'casual',
        cost: 'low',
        difficulty: 'easy',
        indoor_outdoor: 'indoor',
        estimated_duration: 60,
        emoji: '☕',
        week_number: 3,
        source: 'weekly'
      },
      {
        id: 'demo-4',
        title: 'Hiking Together',
        description: 'Explore nature trails and enjoy the outdoors',
        category: 'outdoor',
        cost: 'free',
        difficulty: 'medium',
        indoor_outdoor: 'outdoor',
        estimated_duration: 180,
        emoji: '🥾',
        week_number: 4,
        source: 'weekly'
      },
      {
        id: 'demo-5',
        title: 'Movie Night In',
        description: 'Pick a movie, make popcorn, and snuggle up',
        category: 'relaxing',
        cost: 'low',
        difficulty: 'easy',
        indoor_outdoor: 'indoor',
        estimated_duration: 150,
        emoji: '🍿',
        week_number: 5,
        source: 'weekly'
      },
      {
        id: 'demo-6',
        title: 'Art Gallery Visit',
        description: 'Explore local art and discuss your favorites',
        category: 'cultural',
        cost: 'medium',
        difficulty: 'easy',
        indoor_outdoor: 'indoor',
        estimated_duration: 120,
        emoji: '🎨',
        week_number: 6,
        source: 'weekly'
      }
    ];

    return demoData.map(idea => this.transformToDateNightIdea(idea, 'global'));
  }

  /**
   * Get all available date night ideas from both global and user tables
   */
  async getAllIdeas(userId?: string): Promise<DateNightIdea[]> {
    try {
      // Fetch global ideas
      const { data: globalIdeas, error: globalError } = await supabase
        .from('date_night_ideas_global')
        .select('*')
        .order('category, title');

      if (globalError) {
        logger.error('Error fetching global date night ideas:', globalError);
        // Return demo data as fallback
        logger.info('Using demo date night ideas as fallback');
        return this.getDemoIdeas();
      }

      // If no global ideas found, use demo data
      if (!globalIdeas || globalIdeas.length === 0) {
        logger.info('No global date night ideas found in database, using demo data');
        return this.getDemoIdeas();
      }

      logger.info('Found global date night ideas:', globalIdeas.length);

      // Fetch user ideas if userId provided
      let userIdeas: any[] = [];
      if (userId) {
        const { data: userIdeasData, error: userError } = await supabase
          .from('date_night_ideas_user')
          .select('*')
          .eq('user_id', userId)
          .order('category, title');

        if (userError) {
          logger.error('Error fetching user date night ideas:', userError);
          // Don't throw here, just log the error and continue with global ideas
        } else {
          userIdeas = userIdeasData || [];
        }
      }

      // Transform and combine both sources
      const transformedGlobalIdeas = (globalIdeas || []).map(idea =>
        this.transformToDateNightIdea(idea, 'global')
      );

      const transformedUserIdeas = userIdeas.map(idea =>
        this.transformToDateNightIdea(idea, 'user')
      );

      const allIdeas = [...transformedGlobalIdeas, ...transformedUserIdeas];

      logger.info(`Loaded ${transformedGlobalIdeas.length} global and ${transformedUserIdeas.length} user date night ideas`);
      return allIdeas;
    } catch (error) {
      logger.error('Error in getAllIdeas:', error);
      // Return demo data as final fallback
      logger.info('Using demo date night ideas as error fallback');
      return this.getDemoIdeas();
    }
  }

  /**
   * Get a specific date night idea by composite ID
   */
  async getIdeaById(compositeId: string): Promise<DateNightIdea | null> {
    try {
      const { source, id } = this.parseCompositeId(compositeId);

      const tableName = source === 'global' ? 'date_night_ideas_global' : 'date_night_ideas_user';

      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .eq('id', id)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        logger.error(`Error fetching ${source} date night idea:`, error);
        throw new Error(`Failed to fetch idea: ${error.message}`);
      }

      if (!data) {
        return null;
      }

      return this.transformToDateNightIdea(data, source);
    } catch (error) {
      logger.error('Error in getIdeaById:', error);
      return null;
    }
  }

  /**
   * Get random date night ideas
   */
  async getRandomIdeas(count: number = 5): Promise<DateNightIdea[]> {
    try {
      const { data, error } = await supabase
        .rpc('get_random_date_night_ideas', { p_limit: count });

      if (error) {
        logger.error('Error fetching random date night ideas:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error fetching random date night ideas:', error);
      return [];
    }
  }

  /**
   * Get date night ideas by category
   */
  async getIdeasByCategory(category: string): Promise<DateNightIdea[]> {
    try {
      const { data, error } = await supabase
        .rpc('get_date_night_ideas_by_category', { p_category: category });

      if (error) {
        logger.error('Error fetching date night ideas by category:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error fetching date night ideas by category:', error);
      return [];
    }
  }

  /**
   * Get date night ideas by difficulty
   */
  async getIdeasByDifficulty(difficulty: 'easy' | 'medium' | 'hard'): Promise<DateNightIdea[]> {
    try {
      const { data, error } = await supabase
        .from('date_night_ideas_global')
        .select('*')
        .eq('difficulty', difficulty);

      if (error) {
        logger.error('Error fetching date night ideas by difficulty:', error);
        return [];
      }

      return (data as any[])?.map(r => this.transformToDateNightIdea(r, 'global')) || [];
    } catch (error) {
      logger.error('Error fetching date night ideas by difficulty:', error);
      return [];
    }
  }

  /**
   * Get weekly date night ideas
   */
  async getWeeklyIdeas(): Promise<DateNightIdea[]> {
    try {
      const { data, error } = await supabase
        .from('date_night_ideas_global')
        .select('*')
        .eq('source', 'weekly')
        .order('week_number');

      if (error) {
        logger.error('Error fetching weekly date night ideas:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error fetching weekly date night ideas:', error);
      return [];
    }
  }



  /**
   * Get user's saved date night ideas
   */
  async getUserIdeas(userId: string): Promise<UserDateNightIdea[]> {
    try {
      const { data, error } = await supabase
        .from('date_night_ideas_user')
        .select(`
          *,
          date_night_ideas_global (
            id,
            title,
            description,
            emoji,
            category,
            difficulty,
            estimated_duration,
            cost_level,
            indoor_outdoor
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('Error fetching user date night ideas:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error fetching user date night ideas:', error);
      return [];
    }
  }

  /**
   * Save a date night idea to user's collection
   */
  async saveUserIdea(userId: string, ideaId: string, status: 'planned' | 'completed' | 'favorite', notes?: string, rating?: number): Promise<boolean> {
    try {
      // Note: The current schema doesn't support status tracking in date_night_ideas_user
      // This would need to be implemented using the favorites table or a separate tracking table
      logger.warn('saveUserIdea called but current schema doesn\'t support status tracking');
      return false;
    } catch (error) {
      logger.error('Error saving user date night idea:', error);
      return false;
    }
  }

  /**
   * Remove a date night idea from user's collection
   */
  async removeUserIdea(userId: string, ideaId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('date_night_ideas_user')
        .delete()
        .eq('user_id', userId)
        .eq('idea_id', ideaId);

      if (error) {
        logger.error('Error removing user date night idea:', error);
        return false;
      }

      logger.info('User date night idea removed successfully');
      return true;
    } catch (error) {
      logger.error('Error removing user date night idea:', error);
      return false;
    }
  }

  /**
   * Get categories
   */
  async getCategories(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('date_night_ideas_global')
        .select('category')
        .not('category', 'is', null);

      if (error) {
        logger.error('Error fetching categories:', error);
        return [];
      }

      const categories = [...new Set((data as any[])?.map(item => item.category).filter(Boolean))];
      return categories.sort();
    } catch (error) {
      logger.error('Error fetching categories:', error);
      return [];
    }
  }

  /**
   * Search date night ideas with proper SQL injection protection
   */
  async searchIdeas(query: string): Promise<DateNightIdea[]> {
    try {
      // Validate and sanitize input using our security utility
      const validation = sanitizeSQLSearch(query);

      if (!validation.isValid || !validation.sanitizedValue) {
        logger.debug('Invalid search query:', validation.error);
        return [];
      }

      const sanitizedQuery = validation.sanitizedValue;

      // Use parameterized query with proper escaping
      const { data, error } = await supabase
        .from('date_night_ideas_global')
        .select('*')
        .or(`title.ilike.%${sanitizedQuery}%,description.ilike.%${sanitizedQuery}%,category.ilike.%${sanitizedQuery}%`)
        .order('title')
        .limit(50); // Prevent excessive results

      if (error) {
        logger.error('Error searching date night ideas:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error searching date night ideas:', error);
      return [];
    }
  }
}

export const dateNightIdeasService = new DateNightIdeasService();
export default dateNightIdeasService;
