/**
 * Daily Questions Hook
 *
 * Custom hook for managing daily questions state and operations.
 * Provides reactive data and methods for the daily questions feature.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import { useCallback, useEffect, useState } from 'react';
import { useUserProfile } from '../../hooks/useUserProfile';
import { logger } from '../../shared/utils/logger';
import { couplePairingService } from '../onboarding/couplePairingService';
import { dailyQuestionsService } from './dailyQuestionsService';
import { useAuth } from './useAuth';
// TODO: Define missing types in daily.types.ts
// import type {
//     CoupleResponseStatus,
//     DailyQuestion,
//     QuestionHistory,
//     StreakData
// } from './daily.types';

// Temporary type definitions
type CoupleResponseStatus = any;
type DailyQuestion = any;
type QuestionHistory = any;
type StreakData = any;

export interface UseDailyQuestionsReturn {
  // Current question data
  todaysQuestion: DailyQuestion | null;
  responseStatus: CoupleResponseStatus[];
  isLoading: boolean;
  error: string | null;

  // User's data
  userResponse: string | null;
  partnerResponse: string | null;
  hasUserAnswered: boolean;
  hasPartnerAnswered: boolean;

  // Streak data
  streakData: StreakData | null;

  // Actions
  submitResponse: (responseText: string) => Promise<boolean>;
  updateResponse: (responseText: string) => Promise<boolean>;
  addReaction: (responseId: string, reactionType: 'heart' | 'laugh' | 'surprise' | 'love') => Promise<boolean>;
  addComment: (responseId: string, commentText: string) => Promise<boolean>;
  skipQuestion: () => Promise<boolean>;
  refreshData: () => Promise<void>;

  // History
  questionHistory: QuestionHistory[];
  loadMoreHistory: () => Promise<void>;
  isHistoryLoading: boolean;
}

export function useDailyQuestions(): UseDailyQuestionsReturn {
  const { user } = useAuth();
  const { profile } = useUserProfile();

  console.log('useDailyQuestions: Hook initialized', { user: !!user, profile: !!profile, coupleId: 'disabled' }); // TODO: Re-enable when coupleId is available

  // State
  const [todaysQuestion, setTodaysQuestion] = useState<DailyQuestion | null>(null);
  const [responseStatus, setResponseStatus] = useState<CoupleResponseStatus[]>([]);
  const [streakData, setStreakData] = useState<StreakData | null>(null);
  const [questionHistory, setQuestionHistory] = useState<QuestionHistory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isHistoryLoading, setIsHistoryLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [historyOffset, setHistoryOffset] = useState(0);

  // Derived state
  const userResponse = responseStatus.find(r => r.user_id === user?.id)?.response_text || null;
  const partnerResponse = responseStatus.find(r => r.user_id !== user?.id)?.response_text || null;
  const hasUserAnswered = responseStatus.find(r => r.user_id === user?.id)?.has_answered || false;
  const hasPartnerAnswered = responseStatus.find(r => r.user_id !== user?.id)?.has_answered || false;

  // Helper function to get couple ID from user ID
  const getCoupleId = useCallback(async (): Promise<string> => {
    if (!user?.id) return 'dev-couple-123';

    try {
      const couple = await couplePairingService.getUserCouple(user.id);
      return couple?.id || 'dev-couple-123';
    } catch (error) {
      logger.error('Error getting couple ID:', error);
      return 'dev-couple-123';
    }
  }, [user?.id]);

  // Load initial data
  const loadData = useCallback(async () => {
    // TODO: Fix coupleId access - UserProfile doesn't have coupleId property
    console.log('useDailyQuestions: loadData called', { user: !!user, coupleId: 'disabled' });

    if (!user) {
      console.log('useDailyQuestions: No user, setting loading to false');
      setIsLoading(false);
      return;
    }

    // TODO: Re-enable when coupleId is properly available
    /*
    // Note: We'll get coupleId inside the function, so no need to check here
    */

    try {
      setIsLoading(true);
      setError(null);

      // Get coupleId from user's couple relationship
      const coupleId = await getCoupleId();
      console.log('useDailyQuestions: Loading data for couple:', coupleId, 'user:', user?.id);

      // Load today's question and response status in parallel
      const [question, status, streak] = await Promise.allSettled([
        dailyQuestionsService.getTodaysQuestion(coupleId),
        dailyQuestionsService.getCoupleResponseStatus(coupleId),
        dailyQuestionsService.getStreakData(user.id)
      ]);

      console.log('useDailyQuestions: Data loaded', {
        question: question.status === 'fulfilled' ? question.value : question.reason,
        status: status.status === 'fulfilled' ? status.value : status.reason,
        streak: streak.status === 'fulfilled' ? streak.value : streak.reason
      });

      // Handle results with proper error handling
      setTodaysQuestion(question.status === 'fulfilled' ? question.value : null);
      setResponseStatus(status.status === 'fulfilled' ? status.value : []);
      setStreakData(streak.status === 'fulfilled' ? streak.value : null);

      // Load initial history
      const history = await dailyQuestionsService.getQuestionHistory(
        coupleId,
        user.id,
        10,
        0
      );
      setQuestionHistory(history);
      setHistoryOffset(10);

    } catch (err) {
      logger.error('Error loading daily questions data:', err);
      setError('Failed to load daily questions');
    } finally {
      setIsLoading(false);
    }
  }, [user, getCoupleId]);

  // Load more history
  const loadMoreHistory = useCallback(async () => {
    if (!user || isHistoryLoading) return;

    const coupleId = await getCoupleId();

    try {
      setIsHistoryLoading(true);

      const moreHistory = await dailyQuestionsService.getQuestionHistory(
        coupleId,
        user.id,
        10,
        historyOffset
      );

      setQuestionHistory(prev => [...prev, ...moreHistory]);
      setHistoryOffset(prev => prev + 10);

    } catch (err) {
      logger.error('Error loading more history:', err);
    } finally {
      setIsHistoryLoading(false);
    }
  }, [user, getCoupleId, historyOffset, isHistoryLoading]);

  // Submit response
  const submitResponse = useCallback(async (responseText: string): Promise<boolean> => {
    if (!user || !todaysQuestion) return false;

    const coupleId = await getCoupleId();

    try {
      const response = await dailyQuestionsService.submitResponse(
        user.id,
        coupleId,
        todaysQuestion.question_id,
        responseText
      );

      if (response) {
        // Refresh data to show updated response
        await loadData();
        return true;
      }

      return false;
    } catch (err) {
      logger.error('Error submitting response:', err);
      return false;
    }
  }, [user, getCoupleId, todaysQuestion, loadData]);

  // Update response
  const updateResponse = useCallback(async (responseText: string): Promise<boolean> => {
    if (!user || !responseStatus) return false;

    const userResponseData = responseStatus.find(r => r.user_id === user.id);
    if (!userResponseData || !userResponseData.has_answered) return false;

    try {
      // Find the response ID from the current data
      const responseId = questionHistory
        .find(h => h.user_response?.user_id === user.id)
        ?.user_response?.id;

      if (!responseId) return false;

      const response = await dailyQuestionsService.updateResponse(
        responseId,
        responseText,
        user.id
      );

      if (response) {
        // Refresh data to show updated response
        await loadData();
        return true;
      }

      return false;
    } catch (err) {
      logger.error('Error updating response:', err);
      return false;
    }
  }, [user, responseStatus, questionHistory, loadData]);

  // Add reaction
  const addReaction = useCallback(async (
    responseId: string,
    reactionType: 'heart' | 'laugh' | 'surprise' | 'love'
  ): Promise<boolean> => {
    if (!user) return false;

    try {
      const reaction = await dailyQuestionsService.addReaction(
        responseId,
        user.id,
        reactionType
      );

      if (reaction) {
        // Refresh data to show updated reactions
        await loadData();
        return true;
      }

      return false;
    } catch (err) {
      logger.error('Error adding reaction:', err);
      return false;
    }
  }, [user, loadData]);

  // Add comment
  const addComment = useCallback(async (
    responseId: string,
    commentText: string
  ): Promise<boolean> => {
    if (!user) return false;

    try {
      const comment = await dailyQuestionsService.addComment(
        responseId,
        user.id,
        commentText
      );

      if (comment) {
        // Refresh data to show updated comments
        await loadData();
        return true;
      }

      return false;
    } catch (err) {
      logger.error('Error adding comment:', err);
      return false;
    }
  }, [user, loadData]);

  // Skip question
  const skipQuestion = useCallback(async (): Promise<boolean> => {
    if (!user) return false;

    const coupleId = await getCoupleId();

    try {
      const success = await dailyQuestionsService.skipTodaysQuestion(
        user.id,
        coupleId
      );

      if (success) {
        // Refresh data to potentially show a new question
        await loadData();
        return true;
      }

      return false;
    } catch (err) {
      logger.error('Error skipping question:', err);
      return false;
    }
  }, [user, getCoupleId, loadData]);

  // Refresh data
  const refreshData = useCallback(async () => {
    await loadData();
  }, [loadData]);

  // Load data on mount and when dependencies change
  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    // Current question data
    todaysQuestion,
    responseStatus,
    isLoading,
    error,

    // User's data
    userResponse,
    partnerResponse,
    hasUserAnswered,
    hasPartnerAnswered,

    // Streak data
    streakData,

    // Actions
    submitResponse,
    updateResponse,
    addReaction,
    addComment,
    skipQuestion,
    refreshData,

    // History
    questionHistory,
    loadMoreHistory,
    isHistoryLoading,
  };
}
