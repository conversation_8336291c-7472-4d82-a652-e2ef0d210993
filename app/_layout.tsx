import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect } from 'react';
import { FavoritesProvider } from '../src/contexts/FavoritesContext';
import { SettingsProvider } from '../src/contexts/SettingsContext';
import { AuthProvider } from '../src/journeys/onboarding/useAuth';
import { attachAuthFlushListener } from '../src/journeys/progress/eventLogger';
import { GlobalThemeProvider } from '../src/shared/components/common/ThemeProvider';
import { ErrorHandler } from '../src/shared/components/layout/ErrorHandler';

export default function RootLayout() {
  useEffect(() => {
    const detach = attachAuthFlushListener();
    return () => detach?.();
  }, []);

  return (
    <ErrorHandler>
      <SettingsProvider>
        <GlobalThemeProvider>
          <AuthProvider>
            <FavoritesProvider>
              <Stack screenOptions={{ headerShown: false }}>
                <Stack.Screen name="index" />
                <Stack.Screen name="onboarding" />
                <Stack.Screen name="auth" />
                <Stack.Screen name="our-story" />
                <Stack.Screen name="couple-profile" />
                <Stack.Screen name="app-settings" />
                <Stack.Screen name="notifications" />
                <Stack.Screen name="scrapbook" />
                <Stack.Screen name="settings" />
                <Stack.Screen name="daily-questions" />
                <Stack.Screen name="daily-questions-settings" />
                <Stack.Screen name="progress" />
                <Stack.Screen name="(tabs)" />
                <Stack.Screen name="+not-found" />
              </Stack>
              <StatusBar style="auto" />
            </FavoritesProvider>
          </AuthProvider>
        </GlobalThemeProvider>
      </SettingsProvider>
    </ErrorHandler>
  );

  /* Original code with providers - commented out for debugging
  useEffect(() => {
    const detach = attachAuthFlushListener();
    return () => detach?.();
  }, []);

  return (
    <ErrorHandler>
      <SettingsProvider>
        <GlobalThemeProvider>
          <AuthProvider>
            <FavoritesProvider>
              <Stack screenOptions={{ headerShown: false }}>
                <Stack.Screen name="index" />
                <Stack.Screen name="onboarding" />
                <Stack.Screen name="auth" />
                <Stack.Screen name="our-story" />
                <Stack.Screen name="couple-profile" />
                <Stack.Screen name="app-settings" />
                <Stack.Screen name="notifications" />
                <Stack.Screen name="scrapbook" />
                <Stack.Screen name="settings" />
                <Stack.Screen name="daily-questions" />
                <Stack.Screen name="daily-questions-settings" />
                <Stack.Screen name="progress" />
                <Stack.Screen name="(tabs)" />
                <Stack.Screen name="+not-found" />
              </Stack>
              <StatusBar style="auto" />
            </FavoritesProvider>
          </AuthProvider>
        </GlobalThemeProvider>
      </SettingsProvider>
    </ErrorHandler>
  );
  */
}
