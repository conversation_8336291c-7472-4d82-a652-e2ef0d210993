/**
 * Join <PERSON><PERSON>le <PERSON>
 *
 * Allows users to join an existing couple using a pairing code.
 * Includes attempt limiting, validation, and success handling.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */


import { router } from 'expo-router';
import { AlertCircle, CheckCircle, Share2 } from 'lucide-react-native';
import React, { useCallback, useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Image,
    KeyboardAvoidingView,
    Platform,
    Share,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import { icons } from '../src/assets';
import { useAuth } from '../src/hooks/useAuth';
import { useCouplePairing } from '../src/journeys/onboarding/useCouplePairing';
import { logEvent } from '../src/journeys/progress/eventLogger';
import { colors } from '../src/shared/utils/colors';

// Import shared auth components and design system
import {
    AuthScreenLayout,
} from '../src/components/shared/AuthComponents';
import { GlassView } from '../src/shared/components/common/GlassView';

// Constants for better maintainability
const INVITE_CODE_LENGTH = 6;
const SUCCESS_REDIRECT_DELAY = 2000;
const SHARE_SUCCESS_DURATION = 3000;
const FALLBACK_CODE = 'LOVE47';

// Error messages
const ERROR_MESSAGES = {
  EMPTY_CODE: 'Please enter a pairing code',
  INVALID_LENGTH: `Code must be ${INVITE_CODE_LENGTH} characters`,
  MAX_ATTEMPTS: 'Maximum pairing attempts exceeded. Please contact support.',
  INVALID_CODE: 'Invalid code. Please check and try again.',
  NO_CODE_AVAILABLE: 'No invite code available yet. Please wait...',
  SHARE_FAILED: 'Failed to share code. Please try again.',
} as const;

// Enhanced state management with better typing
interface JoinCoupleState {
  partnerCode: string;
  isLoading: boolean;
  error: string | null;
  success: boolean;
  isCreatingCouple: boolean;
  shareSuccess: boolean;
  retryCount: number;
}

export default function JoinCoupleScreen() {
  const [state, setState] = useState<JoinCoupleState>({
    partnerCode: '',
    isLoading: false,
    error: null,
    success: false,
    isCreatingCouple: false,
    shareSuccess: false,
    retryCount: 0,
  });

  const {
    joinCouple,
    pairingStatus,
    checkPairingStatus,
    couple,
    createCouple
  } = useCouplePairing();

  const { isAuthenticated, user } = useAuth();

  // Check pairing status on mount and create couple if needed
  useEffect(() => {
    if (isAuthenticated && user) {
      checkPairingStatus();
    }
  }, [isAuthenticated, user]);

  // Create couple if none exists
  useEffect(() => {
    if (isAuthenticated && user && !couple && !state.isCreatingCouple) {
      setState(prev => ({ ...prev, isCreatingCouple: true }));
      createCouple().finally(() => {
        setState(prev => ({ ...prev, isCreatingCouple: false }));
      });
    }
  }, [isAuthenticated, user, couple, state.isCreatingCouple, createCouple]);

  /**
   * Handles sharing the invite code with partner
   * Uses native Share API with fallback error handling
   */
  const handleShareCode = useCallback(async () => {
    const code = couple?.couple_code;

    if (!code) {
      setState(prev => ({ ...prev, error: ERROR_MESSAGES.NO_CODE_AVAILABLE }));
      return;
    }

    const message = `Join me on Everlasting Us! Use my invite code: ${code}`;

    try {
      setState(prev => ({ ...prev, shareSuccess: false }));

      await Share.share({
        message: message,
        title: 'Join Everlasting Us',
      });

      // Show success feedback
      setState(prev => ({ ...prev, shareSuccess: true }));
      setTimeout(() => {
        setState(prev => ({ ...prev, shareSuccess: false }));
      }, SHARE_SUCCESS_DURATION);

    } catch (error) {
      console.error('Error sharing code:', error);
      setState(prev => ({ ...prev, error: ERROR_MESSAGES.SHARE_FAILED }));
    }
  }, [couple?.couple_code]);

  /**
   * Handles connecting with partner using their invite code
   * Includes validation, attempt limiting, and success handling
   */
  const handleConnect = useCallback(async () => {
    if (!state.partnerCode.trim()) {
      setState(prev => ({ ...prev, error: ERROR_MESSAGES.EMPTY_CODE }));
      return;
    }

    if (state.partnerCode.length !== INVITE_CODE_LENGTH) {
      setState(prev => ({ ...prev, error: ERROR_MESSAGES.INVALID_LENGTH }));
      return;
    }

    if (!pairingStatus?.can_attempt) {
      setState(prev => ({ ...prev, error: ERROR_MESSAGES.MAX_ATTEMPTS }));
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const success = await joinCouple(state.partnerCode.trim());

      if (success) {
        setState(prev => ({ ...prev, success: true }));
        await logEvent('onboarding_partner_invited');

        // Show success message briefly, then navigate
        setTimeout(() => {
          router.replace('/our-story?mode=edit');
        }, SUCCESS_REDIRECT_DELAY);
      }
    } catch (err) {
      setState(prev => ({ ...prev, error: ERROR_MESSAGES.INVALID_CODE }));
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [state.partnerCode, pairingStatus, joinCouple]);

  // Success state
  if (state.success && couple) {
    return (
      <AuthScreenLayout>
        <View style={styles.container}>
          <View style={styles.successContainer}>
            <CheckCircle size={64} color={colors.primary} />
            <Text style={styles.successTitle}>Welcome to the couple!</Text>
            <Text style={styles.successSubtitle}>
              You've successfully joined your partner's journal.
              Let's start your story together!
            </Text>
          </View>
        </View>
      </AuthScreenLayout>
    );
  }

  // Blocked state
  if (pairingStatus?.is_blocked) {
    return (
      <AuthScreenLayout>
        <View style={styles.container}>
          <View style={styles.centeredContent}>
            <AlertCircle size={64} color={colors.error} style={styles.icon} />
            <Text style={styles.title}>Pairing Blocked</Text>
            <Text style={styles.subtitle}>
              You've exceeded the maximum number of pairing attempts. Please contact support if you need assistance.
            </Text>

            <TouchableOpacity
              style={[styles.button, styles.secondaryButton]}
              onPress={() => router.back()}
            >
              <Text style={styles.secondaryButtonText}>Go Back</Text>
            </TouchableOpacity>
          </View>
        </View>
      </AuthScreenLayout>
    );
  }

  // Main pairing screen
  return (
    <AuthScreenLayout>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <View style={styles.centeredContent}>
          {/* App Icon/Logo */}
          <Image source={icons.favicon} style={styles.appIcon} />

          <Text style={styles.title}>Connect with Your Partner</Text>
          <Text style={styles.subtitle}>
            Share your invite code with your partner or enter theirs to start your journey together.
          </Text>

          {/* Your Invite Code Section */}
          <GlassView style={styles.inviteCodeCard} intensity="medium">
            <Text style={styles.cardTitle}>Your Invite Code</Text>
            {state.isCreatingCouple ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={colors.primary} />
                <Text style={styles.loadingText}>Generating code...</Text>
              </View>
            ) : (
              <Text style={styles.inviteCode}>
                {couple?.couple_code || FALLBACK_CODE}
              </Text>
            )}
            <TouchableOpacity
              style={[
                styles.button,
                state.shareSuccess ? styles.successButton : styles.shareButton,
                (!couple?.couple_code && !state.isCreatingCouple) && styles.disabledButton
              ]}
              onPress={handleShareCode}
              disabled={!couple?.couple_code && !state.isCreatingCouple}
              accessibilityLabel="Share your invite code"
              accessibilityHint="Opens sharing options to send your invite code to your partner"
            >
              {state.shareSuccess ? (
                <CheckCircle size={20} color={colors.white} style={styles.buttonIcon} />
              ) : (
                <Share2 size={20} color={colors.white} style={styles.buttonIcon} />
              )}
              <Text style={styles.shareButtonText}>
                {state.shareSuccess ? 'Shared!' : 'Share Code'}
              </Text>
            </TouchableOpacity>
          </GlassView>

          {/* Enter Partner's Code Section */}
          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>Enter Partner's Code</Text>
            <TextInput
              style={[styles.codeInput, state.error && styles.codeInputError]}
              value={state.partnerCode}
              onChangeText={(text) => {
                // Enhanced input validation
                const cleanText = text.replace(/[^A-Z0-9]/g, '').slice(0, INVITE_CODE_LENGTH);
                setState(prev => ({
                  ...prev,
                  partnerCode: cleanText,
                  error: null
                }));
              }}
              placeholder={`Enter ${INVITE_CODE_LENGTH}-character code`}
              placeholderTextColor={colors.textSecondary}
              maxLength={INVITE_CODE_LENGTH}
              autoCapitalize="characters"
              autoCorrect={false}
              autoFocus={false}
              returnKeyType="done"
              onSubmitEditing={handleConnect}
              accessibilityLabel="Partner's invite code input"
              accessibilityHint="Enter the 6-character code your partner shared with you"
              editable={!state.isLoading}
            />

            {pairingStatus && (
              <Text style={styles.attemptsText}>
                {pairingStatus.attempts_remaining} attempts remaining
              </Text>
            )}

            {state.error && (
              <View style={styles.errorContainer}>
                <AlertCircle size={16} color={colors.error} />
                <Text style={styles.errorText}>{state.error}</Text>
              </View>
            )}

            <TouchableOpacity
              style={[
                styles.button,
                styles.connectButton,
                (state.partnerCode.length !== INVITE_CODE_LENGTH || state.isLoading || !pairingStatus?.can_attempt) && styles.disabledButton
              ]}
              onPress={handleConnect}
              disabled={state.partnerCode.length !== INVITE_CODE_LENGTH || state.isLoading || !pairingStatus?.can_attempt}
              accessibilityLabel="Connect with partner"
              accessibilityHint="Attempts to connect with your partner using the entered code"
            >
              <Text style={styles.connectButtonText}>
                {state.isLoading ? 'Connecting...' : 'Connect'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </AuthScreenLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centeredContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  iconPlaceholder: {
    fontSize: 64,
    marginBottom: 32,
  },
  appIcon: {
    width: 64,
    height: 64,
    marginBottom: 32,
    borderRadius: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.white,
    textAlign: 'center',
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    maxWidth: 300,
  },
  inviteCodeCard: {
    borderRadius: 24,
    padding: 24,
    width: '100%',
    marginBottom: 32,
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 16,
    color: colors.white,
    textAlign: 'center',
    marginBottom: 16,
  },
  inviteCode: {
    fontSize: 32,
    color: colors.primary,
    letterSpacing: 4,
    textAlign: 'center',
    marginBottom: 24,
    fontFamily: 'monospace',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
    gap: 8,
  },
  loadingText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  inputSection: {
    width: '100%',
  },
  inputLabel: {
    fontSize: 16,
    color: colors.white,
    marginBottom: 8,
  },
  codeInput: {
    width: '100%',
    padding: 16,
    borderWidth: 2,
    borderColor: colors.primary,
    borderRadius: 12,
    backgroundColor: colors.white,
    textAlign: 'center',
    fontSize: 18,
    letterSpacing: 2,
    marginBottom: 16,
    fontFamily: 'monospace',
  },
  codeInputError: {
    borderColor: colors.error,
  },
  attemptsText: {
    color: colors.textSecondary,
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 8,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: `${colors.error}20`,
    borderRadius: 8,
    gap: 8,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
    textAlign: 'center',
  },
  button: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  shareButton: {
    backgroundColor: colors.secondary,
    width: '100%',
  },
  successButton: {
    backgroundColor: colors.success,
    width: '100%',
  },
  shareButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  connectButton: {
    backgroundColor: colors.primary,
    width: '100%',
  },
  connectButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginTop: 16,
  },
  secondaryButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    backgroundColor: colors.textSecondary,
    opacity: 0.5,
  },
  buttonIcon: {
    marginRight: 4,
  },
  icon: {
    marginBottom: 24,
  },
  successContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  successTitle: {
    color: colors.white,
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginTop: 24,
    marginBottom: 16,
  },
  successSubtitle: {
    color: colors.textSecondary,
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
});
