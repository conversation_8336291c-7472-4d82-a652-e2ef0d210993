/**
 * Join <PERSON>uple Screen
 *
 * Allows users to join an existing couple using a pairing code.
 * Includes attempt limiting, validation, and success handling.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */


import { router } from 'expo-router';
import { AlertCircle, CheckCircle, Share2 } from 'lucide-react-native';
import React, { useEffect, useState } from 'react';
import {
  Image,
  KeyboardAvoidingView,
  Platform,
  Share,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import { icons } from '../src/assets';
import { useAuth } from '../src/hooks/useAuth';
import { useCouplePairing } from '../src/journeys/onboarding/useCouplePairing';
import { logEvent } from '../src/journeys/progress/eventLogger';
import { colors } from '../src/shared/utils/colors';

// Import shared auth components and design system
import {
  AuthScreenLayout,
} from '../src/components/shared/AuthComponents';
import { GlassView } from '../src/shared/components/common/GlassView';

export default function JoinCoupleScreen() {
  const [partnerCode, setPartnerCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const {
    joinCouple,
    pairingStatus,
    checkPairingStatus,
    couple,
    createCouple
  } = useCouplePairing();

  const { isAuthenticated, user } = useAuth();

  // Check pairing status on mount and create couple if needed
  useEffect(() => {
    if (isAuthenticated && user) {
      checkPairingStatus();
    }
  }, [isAuthenticated, user]);

  // Create couple if none exists
  useEffect(() => {
    if (isAuthenticated && user && !couple && !isLoading) {
      createCouple();
    }
  }, [isAuthenticated, user, couple, isLoading, createCouple]);

  const handleShareCode = async () => {
    const code = couple?.couple_code || 'LOVE47'; // Fallback for demo
    const message = `Join me on Everlasting Us! Use my invite code: ${code}`;

    try {
      await Share.share({
        message: message,
        title: 'Join Everlasting Us',
      });
    } catch (error) {
      console.error('Error sharing code:', error);
      // Could show a toast notification here in the future
    }
  };

  const handleConnect = async () => {
    if (!partnerCode.trim()) {
      setError('Please enter a pairing code');
      return;
    }

    if (partnerCode.length !== 6) {
      setError('Code must be 6 characters');
      return;
    }

    if (!pairingStatus?.can_attempt) {
      setError('Maximum pairing attempts exceeded. Please contact support.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const success = await joinCouple(partnerCode.trim());

      if (success) {
        setSuccess(true);
        await logEvent('onboarding_partner_invited');

        // Show success message briefly, then navigate
        setTimeout(() => {
          router.replace('/our-story?mode=edit');
        }, 2000);
      }
    } catch (err) {
      setError('Invalid code. Please check and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Success state
  if (success && couple) {
    return (
      <AuthScreenLayout>
        <View style={styles.container}>
          <View style={styles.successContainer}>
            <CheckCircle size={64} color={colors.primary} />
            <Text style={styles.successTitle}>Welcome to the couple!</Text>
            <Text style={styles.successSubtitle}>
              You've successfully joined your partner's journal.
              Let's start your story together!
            </Text>
          </View>
        </View>
      </AuthScreenLayout>
    );
  }

  // Blocked state
  if (pairingStatus?.is_blocked) {
    return (
      <AuthScreenLayout>
        <View style={styles.container}>
          <View style={styles.centeredContent}>
            <AlertCircle size={64} color={colors.error} style={styles.icon} />
            <Text style={styles.title}>Pairing Blocked</Text>
            <Text style={styles.subtitle}>
              You've exceeded the maximum number of pairing attempts. Please contact support if you need assistance.
            </Text>

            <TouchableOpacity
              style={[styles.button, styles.secondaryButton]}
              onPress={() => router.back()}
            >
              <Text style={styles.secondaryButtonText}>Go Back</Text>
            </TouchableOpacity>
          </View>
        </View>
      </AuthScreenLayout>
    );
  }

  // Main pairing screen
  return (
    <AuthScreenLayout>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <View style={styles.centeredContent}>
          {/* App Icon/Logo */}
          <Image source={icons.favicon} style={styles.appIcon} />

          <Text style={styles.title}>Connect with Your Partner</Text>
          <Text style={styles.subtitle}>
            Share your invite code with your partner or enter theirs to start your journey together.
          </Text>

          {/* Your Invite Code Section */}
          <GlassView style={styles.inviteCodeCard} intensity="medium">
            <Text style={styles.cardTitle}>Your Invite Code</Text>
            <Text style={styles.inviteCode}>
              {couple?.couple_code || (isLoading ? '••••••' : 'LOVE47')}
            </Text>
            <TouchableOpacity
              style={[
                styles.button,
                styles.shareButton,
                (!couple?.couple_code && !isLoading) && styles.disabledButton
              ]}
              onPress={handleShareCode}
              disabled={!couple?.couple_code && !isLoading}
            >
              <Share2 size={20} color={colors.white} style={styles.buttonIcon} />
              <Text style={styles.shareButtonText}>Share Code</Text>
            </TouchableOpacity>
          </GlassView>

          {/* Enter Partner's Code Section */}
          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>Enter Partner's Code</Text>
            <TextInput
              style={[styles.codeInput, error && styles.codeInputError]}
              value={partnerCode}
              onChangeText={(text) => {
                setPartnerCode(text.toUpperCase().slice(0, 6));
                setError(null);
              }}
              placeholder="Enter 6-character code"
              maxLength={6}
              autoCapitalize="characters"
              autoCorrect={false}
            />

            {pairingStatus && (
              <Text style={styles.attemptsText}>
                {pairingStatus.attempts_remaining} attempts remaining
              </Text>
            )}

            {error && (
              <View style={styles.errorContainer}>
                <AlertCircle size={16} color={colors.error} />
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}

            <TouchableOpacity
              style={[
                styles.button,
                styles.connectButton,
                (partnerCode.length !== 6 || isLoading || !pairingStatus?.can_attempt) && styles.disabledButton
              ]}
              onPress={handleConnect}
              disabled={partnerCode.length !== 6 || isLoading || !pairingStatus?.can_attempt}
            >
              <Text style={styles.connectButtonText}>
                {isLoading ? 'Connecting...' : 'Connect'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </AuthScreenLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centeredContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  iconPlaceholder: {
    fontSize: 64,
    marginBottom: 32,
  },
  appIcon: {
    width: 64,
    height: 64,
    marginBottom: 32,
    borderRadius: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.white,
    textAlign: 'center',
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    maxWidth: 300,
  },
  inviteCodeCard: {
    borderRadius: 24,
    padding: 24,
    width: '100%',
    marginBottom: 32,
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 16,
    color: colors.white,
    textAlign: 'center',
    marginBottom: 16,
  },
  inviteCode: {
    fontSize: 32,
    color: colors.primary,
    letterSpacing: 4,
    textAlign: 'center',
    marginBottom: 24,
    fontFamily: 'monospace',
  },
  inputSection: {
    width: '100%',
  },
  inputLabel: {
    fontSize: 16,
    color: colors.white,
    marginBottom: 8,
  },
  codeInput: {
    width: '100%',
    padding: 16,
    borderWidth: 2,
    borderColor: colors.primary,
    borderRadius: 12,
    backgroundColor: colors.white,
    textAlign: 'center',
    fontSize: 18,
    letterSpacing: 2,
    marginBottom: 16,
    fontFamily: 'monospace',
  },
  codeInputError: {
    borderColor: colors.error,
  },
  attemptsText: {
    color: colors.textSecondary,
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 8,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: `${colors.error}20`,
    borderRadius: 8,
    gap: 8,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
    textAlign: 'center',
  },
  button: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  shareButton: {
    backgroundColor: colors.secondary,
    width: '100%',
  },
  shareButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  connectButton: {
    backgroundColor: colors.primary,
    width: '100%',
  },
  connectButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginTop: 16,
  },
  secondaryButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    backgroundColor: colors.textSecondary,
    opacity: 0.5,
  },
  buttonIcon: {
    marginRight: 4,
  },
  icon: {
    marginBottom: 24,
  },
  successContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  successTitle: {
    color: colors.white,
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginTop: 24,
    marginBottom: 16,
  },
  successSubtitle: {
    color: colors.textSecondary,
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
});
