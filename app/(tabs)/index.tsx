/**
 * Home Screen - Clean Version
 *
 * Clean, maintainable version of the home screen using shared components and custom hooks.
 * This demonstrates the final refactored approach with separated concerns.
 *
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import { router } from 'expo-router';
import React, { useEffect, useMemo, useState } from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useDateNightIdeasSupabase } from '../../src/journeys/activities/useDateNightIdeasSupabase';
import { useUserProfile } from '../../src/journeys/onboarding/useUserProfile';
import { useEngagementSystem } from '../../src/journeys/progress/useEngagementSystem';
import { useHomeScreen } from '../../src/journeys/progress/useHomeScreen';
import { usePerformanceTracker, useRenderPerformance } from '../../src/journeys/progress/usePerformance';
import { usePointsSystemSupabase } from '../../src/journeys/progress/usePointsSystemSupabase';
import { useGlobalTheme } from '../../src/shared/components/common/ThemeProvider';
import HamburgerMenu from '../../src/shared/components/layout/HamburgerMenu';
import { CONTENT_CONSTANTS, getPartnerIconEmoji } from '../../src/shared/utils/constants';
import { secureStorage } from '../../src/shared/utils/secureStorage';
import { tokens } from '../../src/shared/utils/theme';

// This function is now replaced by getPartnerIconEmoji from constants
// Import shared components
import {
    AchievementsSection,
    DailyQuestionCard,
    DailyQuestionsStreakCard,
    DSCard,
    FavoritesSection,
    ProfileHeader,
    StatsOverview,
    StreakDisplay,
} from '../../src/components/shared';

export default function HomeScreen() {
  const { getCoupleNames, profile } = useUserProfile();
  const { totalPoints, achievements, level } = usePointsSystemSupabase();
  const { userIdeas } = useDateNightIdeasSupabase();
  const { challengeStreak } = useEngagementSystem();
  const { currentTheme } = useGlobalTheme();
  const [showHamburgerMenu, setShowHamburgerMenu] = useState(false);

  // Performance monitoring
  useRenderPerformance('HomeScreen');
  const { track } = usePerformanceTracker();

  // Initialize home screen utilities (may expand over time)
  useHomeScreen();

  // State for profile pictures (local fallback)
  const [profilePictures, setProfilePictures] = useState<{partner1?: string, partner2?: string}>({});

  // Load local fallback profile pictures on mount
  useEffect(() => {
    loadProfilePictures();
  }, []);

  const loadProfilePictures = async () => {
    try {
      const stored = await secureStorage.getItem<string>('profile_pictures');
      if (stored) {
        setProfilePictures(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Error loading profile pictures:', error);
    }
  };

  // Calculate real statistics
  const currentStreak = challengeStreak?.currentStreak || 0;
  const yearsTogetherCount = calculateYearsTogether();
  const sharedAnswersCount = calculateSharedAnswers();
  // TODO: Fix date night counting - status property doesn't exist on UserDateNightIdea
  const dateNightsCount = 0; // userIdeas?.filter(idea => idea.status === 'completed').length || 0;

  // Helper functions for calculations
  function calculateYearsTogether(): number {
    // Prefer relationshipStartDate if available; fallback to profile.createdAt
    const startIso = profile?.relationshipStartDate;
    const startDate = startIso ? new Date(startIso) : (profile?.createdAt ? new Date(profile.createdAt) : null);
    if (!startDate) return 0;
    const now = new Date();
    const diffYears = now.getFullYear() - startDate.getFullYear() - (
      now < new Date(startDate.getFullYear() + (now.getFullYear() - startDate.getFullYear()), startDate.getMonth(), startDate.getDate()) ? 1 : 0
    );
    return Math.max(0, diffYears);
  }

  function calculateSharedAnswers(): number {
    // This would ideally come from aggregating all week data
    // For now, use engagement stats or a reasonable estimate based on points
    return Math.floor((totalPoints || 0) / 10); // Rough estimate: 10 points per shared answer
  }

  // Memoized calculations for better performance
  const favoriteActivities = useMemo(() => {
    return track('Calculate Favorite Activities', () => {
      // If we have completed date night ideas, use those as favorites
      // TODO: Fix completed ideas filtering - status property doesn't exist on UserDateNightIdea
      const completedIdeas = []; // userIdeas?.filter(idea => idea.status === 'completed' || idea.status === 'favorite').slice(0, 3);
      if (completedIdeas && completedIdeas.length > 0) {
        // This would need to be enhanced to get the actual idea details
        return [...CONTENT_CONSTANTS.DEFAULT_ACTIVITIES]; // For now, return defaults
      }

      return [...CONTENT_CONSTANTS.DEFAULT_ACTIVITIES];
    });
  }, [userIdeas, track]);

  // Get real achievements
  const getRecentAchievements = () => {
    const defaultAchievements = [
      { emoji: '🏆', name: 'Connection Master', desc: `${currentStreak}-day streak completed!` },
      { emoji: '⭐', name: 'Level ' + (level || 1), desc: `Reached level ${level || 1}!` },
    ];

    // Use real achievements if available
    if (achievements && achievements.length > 0) {
      return achievements.slice(0, 2).map(achievement => ({
        emoji: achievement.type === 'streak' ? '🔥' : achievement.type === 'module' ? '⭐' : '🏆',
        name: achievement.title,
        desc: achievement.description,
      }));
    }

    return defaultAchievements;
  };

  return (
    <View style={styles.container}>
      <HamburgerMenu
        position="top-right"
        visible={showHamburgerMenu}
        onClose={() => setShowHamburgerMenu(false)}
        onOpen={() => setShowHamburgerMenu(true)}
      />

      {/* Profile Header */}
      <ProfileHeader
        names={getCoupleNames()}
        subtitle={`Together for ${yearsTogetherCount} beautiful years`}
        leftImage={profile.partner1PhotoUrl || profilePictures.partner1}
        rightImage={profile.partner2PhotoUrl || profilePictures.partner2}
        leftEmoji={getPartnerIconEmoji(profile?.partner1?.icon) || '👩🏻‍🦰'}
        rightEmoji={getPartnerIconEmoji(profile?.partner2?.icon) || '👨🏻‍🦱'}
      />

      {/* Scrollable Content Section */}
      <ScrollView
        style={[styles.content, { backgroundColor: currentTheme.backgroundTertiary }]}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Daily Question Card */}
        <DailyQuestionCard />

        {/* Test Button for Daily Questions */}
        <TouchableOpacity
          style={{
            backgroundColor: currentTheme.primary,
            padding: tokens.spacing.md,
            borderRadius: tokens.radii.xs,
            margin: tokens.spacing.md,
            alignItems: 'center'
          }}
          onPress={() => router.push('/daily-questions')}
        >
          <Text style={{ color: 'white', fontWeight: tokens.typography.fontWeight.bold }}>
            🧪 Test Daily Questions (Debug)
          </Text>
        </TouchableOpacity>

        {/* Daily Questions Streak Card */}
        <DailyQuestionsStreakCard />

        {/* Stats Overview */}
        <StatsOverview
          items={[
            { emoji: '💕', number: yearsTogetherCount, label: 'Years Together', iconBackgroundColor: currentTheme.accentDark },
            { emoji: '💬', number: sharedAnswersCount, label: 'Shared Answers', iconBackgroundColor: currentTheme.primary },
            { emoji: '🔥', number: currentStreak, label: 'Day Streak', iconBackgroundColor: currentTheme.warning },
            { emoji: '🌟', number: dateNightsCount, label: 'Date Nights', iconBackgroundColor: currentTheme.secondary },
          ]}
        />

        {/* Activity Streaks Section */}
        <DSCard style={styles.streakCard}>
          <Text style={[styles.streakCardTitle, { color: currentTheme.textPrimary }]}>
            Your Activity Streaks
          </Text>

          <View style={styles.streakGrid}>
            <View style={styles.streakItem}>
              <Text style={[styles.streakLabel, { color: currentTheme.textSecondary }]}>
                Overall Streak
              </Text>
              <StreakDisplay
                size="medium"
                showIcon={true}
                refreshInterval={60000}
              />
            </View>

            <View style={styles.streakItem}>
              <Text style={[styles.streakLabel, { color: currentTheme.textSecondary }]}>
                Daily Questions
              </Text>
              <StreakDisplay
                category="daily_questions"
                size="medium"
                showIcon={true}
                refreshInterval={60000}
              />
            </View>
          </View>
        </DSCard>

        {/* Favorites Section */}
        <FavoritesSection
          items={favoriteActivities}
        />

        {/* Achievements Section */}
        <AchievementsSection
          items={getRecentAchievements()}
        />
      </ScrollView>
    </View>
  );
}



const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5', // TODO: Replace with theme background
  },
  content: {
    flex: 1,
    borderTopLeftRadius: tokens.radii.xl,
    borderTopRightRadius: tokens.radii.xl,
    marginTop: 0,
  },
  scrollContent: {
    padding: tokens.spacing.xxl,
    paddingBottom: tokens.spacing.xxxl,
  },
  streakCard: {
    marginBottom: tokens.spacing.lg,
  },
  streakCardTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: tokens.spacing.md,
  },
  streakGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  streakItem: {
    alignItems: 'center',
  },
  streakLabel: {
    fontSize: 14,
    marginBottom: tokens.spacing.xs,
  },
});
