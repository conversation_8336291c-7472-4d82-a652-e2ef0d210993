/**
 * Progress Screen
 *
 * Comprehensive progress tracking for all relationship activities including:
 * - Daily questions completion and streaks
 * - Activities and games progress
 * - Achievements and milestones
 * - Connection metrics and trends
 *
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import { router } from 'expo-router';
import {
    ArrowLeft,
    Gamepad2,
    Heart,
    MessageCircle,
    Star,
    Target,
    Trophy
} from 'lucide-react-native';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Animated,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { DSCard } from '../src/components/shared';
import { useDailyQuestions } from '../src/journeys/daily/useDailyQuestions';
import { useProgressData } from '../src/journeys/progress/useProgressData';
import { useGlobalTheme as useTheme } from '../src/shared/components/common/ThemeProvider';
import HamburgerMenu from '../src/shared/components/layout/HamburgerMenu';
import { colors } from '../src/shared/utils/colors';
import { tokens } from '../src/utils/theme';

interface Achievement {
  id: string;
  title: string;
  description: string;
  emoji: string;
  icon: React.ReactNode;
  unlocked: boolean;
  unlockedAt?: string;
  progress?: number;
  maxProgress?: number;
  category: 'questions' | 'activities' | 'games' | 'connection' | 'milestone';
}

interface ProgressCardProps {
  title: string;
  value: number | string;
  subtitle: string;
  color: string;
  icon: string;
}

interface StreakDayProps {
  day: string;
  completed: boolean;
  isToday?: boolean;
}

// Progress Card Component
function ProgressCard({ title, value, subtitle, color, icon }: ProgressCardProps) {
  const { currentTheme } = useTheme();
  return (
    <View style={[styles.progressCard, { backgroundColor: color }]}>
      <View style={styles.progressCardHeader}>
        <Text style={styles.progressCardIcon}>{icon}</Text>
        <Text style={[styles.progressCardValue, { color: currentTheme.textPrimary }]}>{value}</Text>
      </View>
      <Text style={[styles.progressCardTitle, { color: currentTheme.textPrimary }]}>{title}</Text>
      <Text style={[styles.progressCardSubtitle, { color: currentTheme.textPrimary, opacity: 0.7 }]}>{subtitle}</Text>
    </View>
  );
}

// Streak Day Component
function StreakDay({ day, completed, isToday = false }: StreakDayProps) {
  const { currentTheme } = useTheme();
  return (
    <View style={styles.streakDay}>
      <Text style={[styles.streakDayLabel, { color: currentTheme.textPrimary, opacity: 0.7 }]}>{day}</Text>
      <View style={[
        styles.streakDayCircle,
        {
          backgroundColor: isToday
            ? colors.primary
            : completed
              ? colors.primary
              : currentTheme.border,
          borderWidth: isToday ? 2 : 0,
          borderColor: isToday ? colors.primary : 'transparent'
        }
      ]}>
        <Text style={[styles.streakDayText, {
          color: completed || isToday ? 'white' : currentTheme.textSecondary
        }]}>
          {completed ? '✓' : '○'}
        </Text>
      </View>
    </View>
  );
}

export default function ProgressScreen() {
  const { currentTheme } = useTheme();
  const { isLoading } = useDailyQuestions();

  // Use the comprehensive progress data hook
  const {
    metrics: progressData,
    weeklyProgress: weekDays,
    monthlyTrends: monthlyData,
    achievements,
    isLoading: progressLoading,
    error: progressError
  } = useProgressData();

  const [selectedCategory, setSelectedCategory] = useState<'all' | Achievement['category']>('all');
  const [animatedValues] = useState(() =>
    Array.from({ length: 20 }, () => new Animated.Value(0))
  );

  // Filter achievements based on selected category
  const filteredAchievements = achievements.filter(achievement =>
    selectedCategory === 'all' || achievement.category === selectedCategory
  );

  const unlockedAchievements = achievements.filter(a => a.unlocked);
  const lockedAchievements = achievements.filter(a => !a.unlocked);

  // Animate achievements on load
  useEffect(() => {
    const animations = animatedValues.map((value, index) =>
      Animated.timing(value, {
        toValue: 1,
        duration: 300,
        delay: index * 100,
        useNativeDriver: true,
      })
    );

    Animated.stagger(100, animations).start();
  }, []);

  // Show loading state if either data source is loading
  const isLoadingData = isLoading || progressLoading;

  const getCategoryColor = (category: Achievement['category']) => {
    switch (category) {
      case 'questions': return currentTheme.primary;
      case 'activities': return currentTheme.secondary;
      case 'games': return currentTheme.warning;
      case 'connection': return currentTheme.error;
      case 'milestone': return currentTheme.success;
      default: return currentTheme.textSecondary;
    }
  };

  const getCategoryIcon = (category: Achievement['category']) => {
    switch (category) {
      case 'questions': return <MessageCircle size={16} color={currentTheme.primary} />;
      case 'activities': return <Target size={16} color={currentTheme.secondary} />;
      case 'games': return <Gamepad2 size={16} color={currentTheme.warning} />;
      case 'connection': return <Heart size={16} color={currentTheme.error} />;
      case 'milestone': return <Trophy size={16} color={currentTheme.success} />;
      default: return <Star size={16} color={currentTheme.textSecondary} />;
    }
  };

  if (isLoadingData) {
    return (
      <View style={[styles.container, { backgroundColor: currentTheme.background }]}>
        <HamburgerMenu visible={false} onClose={() => {}} menuItems={[]} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={currentTheme.primary} />
          <Text style={[styles.loadingText, { color: currentTheme.textSecondary }]}>
            Loading progress...
          </Text>
        </View>
      </View>
    );
  }

  // Show error state if there's an error and no data
  if (progressError && !progressData) {
    return (
      <View style={[styles.container, { backgroundColor: currentTheme.background }]}>
        <HamburgerMenu visible={false} onClose={() => {}} menuItems={[]} />
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: currentTheme.error }]}>
            Error loading progress: {progressError}
          </Text>
        </View>
      </View>
    );
  }

  // Don't render if no progress data is available
  if (!progressData) {
    return (
      <View style={[styles.container, { backgroundColor: currentTheme.background }]}>
        <HamburgerMenu visible={false} onClose={() => {}} menuItems={[]} />
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: currentTheme.textSecondary }]}>
            No progress data available
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.background }]}>
      <HamburgerMenu visible={false} onClose={() => {}} menuItems={[]} />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color={currentTheme.textPrimary} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: currentTheme.textPrimary }]}>
          Progress
        </Text>
        <View style={styles.headerRight}>
          <View style={[styles.profileAvatar, { backgroundColor: colors.lavender }]}>
            <Text style={styles.profileInitials}>AM</Text>
          </View>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Subtitle */}
        <Text style={[styles.subtitle, { color: currentTheme.textSecondary }]}>
          Track your relationship journey together
        </Text>

        {/* Current Streak Highlight */}
        <View style={[styles.streakHighlight, { backgroundColor: colors.primary }]}>
          <Text style={styles.streakEmoji}>🔥</Text>
          <Text style={styles.streakNumber}>{progressData.questions.current_streak}</Text>
          <Text style={styles.streakLabel}>Day Streak!</Text>
          <Text style={styles.streakSubtext}>
            Your longest streak: {progressData.questions.longest_streak} days
          </Text>
        </View>

        {/* Weekly Progress */}
        <DSCard style={styles.weeklyCard}>
          <Text style={[styles.sectionTitle, { color: currentTheme.textPrimary }]}>This Week</Text>
          <View style={styles.weeklyProgress}>
            {weekDays.map((dayData, index) => (
              <StreakDay
                key={index}
                day={dayData.day}
                completed={dayData.completed}
                isToday={dayData.isToday}
              />
            ))}
          </View>
          <Text style={[styles.weeklySubtext, { color: currentTheme.textSecondary }]}>
            3 of 7 days completed this week
          </Text>
        </DSCard>

        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          <ProgressCard
            title="Questions Answered"
            value={progressData.questions.total_answered.toString()}
            subtitle={`This month: ${progressData.questions.this_month}`}
            color={colors.accentPink}
            icon="❓"
          />
          <ProgressCard
            title="Activities Done"
            value={progressData.activities.total_completed.toString()}
            subtitle={`This month: ${progressData.activities.this_month}`}
            color={colors.lavender}
            icon="🎯"
          />
          <ProgressCard
            title="Games Played"
            value={progressData.games.total_played.toString()}
            subtitle={`Best score: ${progressData.games.best_score}%`}
            color={colors.darkerPink}
            icon="🎮"
          />
          <ProgressCard
            title="Connection Score"
            value={`${progressData.connection.score}%`}
            subtitle={`Up ${progressData.connection.weekly_change}% this week`}
            color={colors.accentPink}
            icon="💕"
          />
        </View>

        {/* Monthly Trends */}
        <DSCard style={styles.trendsCard}>
          <Text style={[styles.sectionTitle, { color: currentTheme.textPrimary }]}>Monthly Trends</Text>
          <View style={styles.trendsContainer}>
            {monthlyData.map((data, index) => (
              <View key={index} style={styles.trendItem}>
                <Text style={[styles.trendMonth, { color: currentTheme.textPrimary }]}>{data.month}</Text>
                <View style={styles.trendMetrics}>
                  <View style={styles.trendMetric}>
                    <View style={[styles.trendDot, { backgroundColor: colors.primary }]} />
                    <Text style={[styles.trendText, { color: currentTheme.textPrimary }]}>
                      {data.questions} questions
                    </Text>
                  </View>
                  <View style={styles.trendMetric}>
                    <View style={[styles.trendDot, { backgroundColor: colors.lavender }]} />
                    <Text style={[styles.trendText, { color: currentTheme.textPrimary }]}>
                      {data.activities} activities
                    </Text>
                  </View>
                </View>
              </View>
            ))}
          </View>
        </DSCard>

        {/* Recent Achievements */}
        <View style={[styles.achievementsHighlight, { backgroundColor: colors.accentPink }]}>
          <Text style={[styles.sectionTitle, { color: currentTheme.textPrimary }]}>Recent Achievements</Text>
          <View style={styles.achievementsList}>
            <View style={styles.achievementItem}>
              <Text style={styles.achievementEmoji}>🏆</Text>
              <Text style={[styles.achievementText, { color: currentTheme.textPrimary }]}>
                Two Week Streak Master
              </Text>
            </View>
            <View style={styles.achievementItem}>
              <Text style={styles.achievementEmoji}>🎯</Text>
              <Text style={[styles.achievementText, { color: currentTheme.textPrimary }]}>
                Activity Explorer (Completed 20+ activities)
              </Text>
            </View>
            <View style={styles.achievementItem}>
              <Text style={styles.achievementEmoji}>💬</Text>
              <Text style={[styles.achievementText, { color: currentTheme.textPrimary }]}>
                Deep Conversationalist (50+ questions)
              </Text>
            </View>
          </View>
        </View>

        {/* Category Filter */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoryScroll}
          contentContainerStyle={styles.categoryContainer}
        >
          <TouchableOpacity
            style={[
              styles.categoryPill,
              selectedCategory === 'all' && styles.categoryPillActive
            ]}
            onPress={() => setSelectedCategory('all')}
          >
            <Text style={[
              styles.categoryPillText,
              selectedCategory === 'all' && styles.categoryPillTextActive
            ]}>
              All ({achievements.length})
            </Text>
          </TouchableOpacity>

          {['questions', 'activities', 'games', 'connection', 'milestone'].map((category) => {
            const count = achievements.filter(a => a.category === category).length;
            return (
              <TouchableOpacity
                key={category}
                style={[
                  styles.categoryPill,
                  selectedCategory === category && styles.categoryPillActive
                ]}
                onPress={() => setSelectedCategory(category as Achievement['category'])}
              >
                <Text style={[
                  styles.categoryPillText,
                  selectedCategory === category && styles.categoryPillTextActive
                ]}>
                  {category.charAt(0).toUpperCase() + category.slice(1)} ({count})
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>

        {/* Achievements List */}
        <View style={styles.achievementsList}>
          {filteredAchievements.map((achievement, index) => (
            <Animated.View
              key={achievement.id}
              style={[
                styles.achievementCard,
                {
                  opacity: animatedValues[index] || 0,
                  transform: [{
                    translateY: animatedValues[index]?.interpolate({
                      inputRange: [0, 1],
                      outputRange: [50, 0],
                    }) || 0
                  }]
                }
              ]}
            >
              <DSCard style={{
                ...styles.achievementCardInner,
                ...(!achievement.unlocked && styles.achievementCardLocked)
              }}>
                <View style={styles.achievementHeader}>
                  <View style={styles.achievementIcon}>
                    {achievement.unlocked ? (
                      <Text style={styles.achievementEmoji}>{achievement.emoji}</Text>
                    ) : (
                      <View style={[styles.lockedIcon, { backgroundColor: currentTheme.border }]}>
                        <Text style={styles.lockedEmoji}>🔒</Text>
                      </View>
                    )}
                  </View>

                  <View style={styles.achievementInfo}>
                    <Text style={[
                      styles.achievementTitle,
                      { color: achievement.unlocked ? currentTheme.textPrimary : currentTheme.textSecondary }
                    ]}>
                      {achievement.title}
                    </Text>
                    <Text style={[
                      styles.achievementDescription,
                      { color: currentTheme.textSecondary }
                    ]}>
                      {achievement.description}
                    </Text>
                  </View>

                  <View style={styles.achievementCategory}>
                    {getCategoryIcon(achievement.category)}
                  </View>
                </View>

                {/* Progress Bar */}
                {achievement.progress !== undefined && achievement.maxProgress && (
                  <View style={styles.progressContainer}>
                    <View style={[styles.progressBar, { backgroundColor: currentTheme.border }]}>
                      <View
                        style={[
                          styles.progressFill,
                          {
                            backgroundColor: getCategoryColor(achievement.category),
                            width: `${(achievement.progress / achievement.maxProgress) * 100}%`
                          }
                        ]}
                      />
                    </View>
                    <Text style={[styles.progressText, { color: currentTheme.textSecondary }]}>
                      {achievement.progress}/{achievement.maxProgress}
                    </Text>
                  </View>
                )}
              </DSCard>
            </Animated.View>
          ))}
        </View>

        {/* Motivation Message */}
        <DSCard style={styles.motivationCard}>
          <View style={styles.motivationContent}>
            <Text style={styles.motivationEmoji}>💕</Text>
            <Text style={[styles.motivationTitle, { color: currentTheme.textPrimary }]}>
              Keep Going!
            </Text>
            <Text style={[styles.motivationText, { color: currentTheme.textSecondary }]}>
              Every question you answer together strengthens your bond.
              {lockedAchievements.length > 0 && ` You're ${lockedAchievements.length} achievements away from unlocking more rewards!`}
            </Text>
          </View>
        </DSCard>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: tokens.spacing.md,
    fontSize: tokens.typography.fontSize.md,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: tokens.spacing.lg,
    paddingVertical: tokens.spacing.md,
  },
  backButton: {
    padding: tokens.spacing.sm,
  },
  headerTitle: {
    flex: 1,
    fontSize: tokens.typography.fontSize.lg,
    fontWeight: tokens.typography.fontWeight.semibold,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: tokens.spacing.lg,
  },
  statsCard: {
    marginBottom: tokens.spacing.lg,
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing.lg,
  },
  statsTitle: {
    marginLeft: tokens.spacing.sm,
    fontSize: tokens.typography.fontSize.md,
    fontWeight: tokens.typography.fontWeight.semibold,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: tokens.spacing.md,
    marginBottom: tokens.spacing.lg,
  },
  statItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: tokens.spacing.md,
  },
  statNumber: {
    fontSize: tokens.typography.fontSize.xl,
    fontWeight: tokens.typography.fontWeight.bold,
    marginBottom: tokens.spacing.xs,
  },
  statLabel: {
    fontSize: tokens.typography.fontSize.sm,
    textAlign: 'center',
  },
  categoryScroll: {
    marginBottom: tokens.spacing.lg,
  },
  categoryContainer: {
    paddingHorizontal: 0,
    gap: tokens.spacing.sm,
  },
  categoryPill: {
    backgroundColor: 'rgba(0,0,0,0.1)',
    paddingHorizontal: tokens.spacing.md,
    paddingVertical: tokens.spacing.sm,
    borderRadius: tokens.radii.pill,
  },
  categoryPillActive: {
    backgroundColor: colors.primary,
  },
  categoryPillText: {
    fontSize: tokens.typography.fontSize.sm,
    fontWeight: tokens.typography.fontWeight.medium,
    color: colors.textSecondary,
  },
  categoryPillTextActive: {
    color: 'white',
  },
  achievementsList: {
    gap: tokens.spacing.md,
    marginBottom: tokens.spacing.lg,
  },
  achievementCard: {
    // Animation container
  },
  achievementCardInner: {
    // Card content
  },
  achievementCardLocked: {
    opacity: 0.6,
  },
  achievementHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  achievementIcon: {
    marginRight: tokens.spacing.md,
  },
  achievementEmoji: {
    fontSize: 32,
  },
  lockedIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  lockedEmoji: {
    fontSize: 16,
  },
  achievementInfo: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: tokens.typography.fontSize.md,
    fontWeight: tokens.typography.fontWeight.semibold,
    marginBottom: tokens.spacing.xs,
  },
  achievementDescription: {
    fontSize: tokens.typography.fontSize.sm,
    lineHeight: tokens.typography.lineHeight.tight,
  },
  achievementCategory: {
    marginLeft: tokens.spacing.sm,
  },
  progressContainer: {
    marginTop: tokens.spacing.md,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: tokens.spacing.xs,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: tokens.typography.fontSize.xs,
    textAlign: 'right',
  },
  motivationCard: {
    marginBottom: tokens.spacing.xl,
  },
  motivationContent: {
    alignItems: 'center',
    textAlign: 'center',
  },
  motivationEmoji: {
    fontSize: 48,
    marginBottom: tokens.spacing.md,
  },
  motivationTitle: {
    fontSize: tokens.typography.fontSize.lg,
    fontWeight: tokens.typography.fontWeight.semibold,
    marginBottom: tokens.spacing.sm,
  },
  motivationText: {
    fontSize: tokens.typography.fontSize.md,
    lineHeight: tokens.typography.lineHeight.normal,
    textAlign: 'center',
  },
  // New Progress Screen Styles
  profileAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileInitials: {
    color: 'white',
    fontSize: tokens.typography.fontSize.sm,
    fontWeight: tokens.typography.fontWeight.semibold,
  },
  subtitle: {
    fontSize: tokens.typography.fontSize.md,
    marginBottom: tokens.spacing.lg,
    paddingHorizontal: 0,
  },
  streakHighlight: {
    padding: tokens.spacing.xl,
    borderRadius: tokens.radii.xl,
    alignItems: 'center',
    marginBottom: tokens.spacing.lg,
  },
  streakEmoji: {
    fontSize: 40,
    marginBottom: tokens.spacing.sm,
  },
  streakNumber: {
    fontSize: 48,
    fontWeight: tokens.typography.fontWeight.bold,
    color: 'white',
    marginBottom: tokens.spacing.xs,
  },
  streakLabel: {
    fontSize: tokens.typography.fontSize.lg,
    color: 'white',
    marginBottom: tokens.spacing.xs,
    opacity: 0.9,
  },
  streakSubtext: {
    fontSize: tokens.typography.fontSize.sm,
    color: 'white',
    opacity: 0.8,
  },
  weeklyCard: {
    marginBottom: tokens.spacing.lg,
  },
  sectionTitle: {
    fontSize: tokens.typography.fontSize.md,
    fontWeight: tokens.typography.fontWeight.semibold,
    marginBottom: tokens.spacing.md,
  },
  weeklyProgress: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: tokens.spacing.md,
  },
  weeklySubtext: {
    fontSize: tokens.typography.fontSize.sm,
    textAlign: 'center',
  },
  streakDay: {
    alignItems: 'center',
    gap: tokens.spacing.sm,
  },
  streakDayLabel: {
    fontSize: tokens.typography.fontSize.xs,
  },
  streakDayCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  streakDayText: {
    fontSize: tokens.typography.fontSize.sm,
  },
  progressCard: {
    width: '48%',
    padding: tokens.spacing.md,
    borderRadius: tokens.radii.lg,
  },
  progressCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: tokens.spacing.sm,
  },
  progressCardIcon: {
    fontSize: 24,
  },
  progressCardValue: {
    fontSize: 24,
    fontWeight: tokens.typography.fontWeight.bold,
  },
  progressCardTitle: {
    fontSize: tokens.typography.fontSize.sm,
    marginBottom: tokens.spacing.xs,
  },
  progressCardSubtitle: {
    fontSize: tokens.typography.fontSize.xs,
  },
  trendsCard: {
    marginBottom: tokens.spacing.lg,
  },
  trendsContainer: {
    gap: tokens.spacing.md,
  },
  trendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  trendMonth: {
    fontSize: tokens.typography.fontSize.sm,
  },
  trendMetrics: {
    flexDirection: 'row',
    gap: tokens.spacing.md,
  },
  trendMetric: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: tokens.spacing.xs,
  },
  trendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  trendText: {
    fontSize: tokens.typography.fontSize.sm,
  },
  achievementsHighlight: {
    padding: tokens.spacing.md,
    borderRadius: tokens.radii.lg,
    marginBottom: tokens.spacing.lg,
  },
  achievementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: tokens.spacing.sm,
    marginBottom: tokens.spacing.sm,
  },
  achievementText: {
    fontSize: tokens.typography.fontSize.sm,
    flex: 1,
  },
});
